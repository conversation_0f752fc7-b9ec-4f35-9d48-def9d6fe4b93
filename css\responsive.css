/* Responsive Design - Mobile First Approach */

/* Large Desktop (1920px+) */
@media (min-width: 1920px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-header h2 {
        font-size: 3.5rem;
    }
}

/* Desktop (1280px - 1919px) */
@media (max-width: 1279px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
}

/* Large Tablet (1024px - 1279px) */
@media (max-width: 1023px) {
    .container {
        padding: 0 var(--spacing-6);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.25rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-6);
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-6);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px - 1023px) */
@media (max-width: 767px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-8) 0;
        gap: var(--spacing-4);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    /* Hero Section */
    .hero {
        padding: calc(80px + var(--spacing-16)) 0 var(--spacing-16);
    }
    
    .hero-container {
        gap: var(--spacing-10);
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    /* Sections */
    .services-preview,
    .about-preview,
    .testimonials,
    .cta {
        padding: var(--spacing-16) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-12);
    }
    
    .section-header h2 {
        font-size: 1.875rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .service-card {
        padding: var(--spacing-6);
    }
    
    .about-text h2 {
        font-size: 1.875rem;
    }
    
    .about-text p {
        font-size: var(--font-size-base);
    }
    
    .features {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .testimonial-card {
        padding: var(--spacing-6);
    }
    
    .testimonial-content p {
        font-size: var(--font-size-base);
    }
    
    .testimonial-author {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }
    
    .cta-content h2 {
        font-size: 1.875rem;
    }
    
    .cta-content p {
        font-size: var(--font-size-base);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-12) 0 var(--spacing-6);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }
    
    .footer-section {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .contact-info {
        align-items: center;
    }
    
    .contact-item {
        text-align: left;
        max-width: 280px;
    }
}

/* Mobile (320px - 767px) */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--spacing-4);
    }
    
    /* Navigation */
    .nav-container {
        padding: 0 var(--spacing-4);
    }
    
    .nav-logo a {
        font-size: var(--font-size-lg);
    }
    
    .nav-logo i {
        font-size: var(--font-size-xl);
    }
    
    /* Hero */
    .hero {
        padding: calc(80px + var(--spacing-12)) 0 var(--spacing-12);
    }
    
    .hero-title {
        font-size: 1.75rem;
        line-height: 1.3;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
    }
    
    .hero-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .btn {
        padding: var(--spacing-4) var(--spacing-6);
        font-size: var(--font-size-sm);
    }
    
    /* Sections */
    .services-preview,
    .about-preview,
    .testimonials,
    .cta {
        padding: var(--spacing-12) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-10);
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
    
    .section-header p {
        font-size: var(--font-size-sm);
    }
    
    .service-card {
        padding: var(--spacing-5);
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-4);
    }
    
    .service-icon i {
        font-size: var(--font-size-xl);
    }
    
    .service-card h3 {
        font-size: var(--font-size-lg);
    }
    
    .service-card p {
        font-size: var(--font-size-sm);
    }
    
    .about-text h2 {
        font-size: 1.5rem;
    }
    
    .about-text p {
        font-size: var(--font-size-sm);
    }
    
    .testimonial-card {
        padding: var(--spacing-5);
    }
    
    .testimonial-content p {
        font-size: var(--font-size-sm);
    }
    
    .cta-content h2 {
        font-size: 1.5rem;
    }
    
    .cta-content p {
        font-size: var(--font-size-sm);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-10) 0 var(--spacing-5);
    }
    
    .footer-logo {
        font-size: var(--font-size-lg);
    }
    
    .footer-logo i {
        font-size: var(--font-size-xl);
    }
    
    .footer-section p {
        font-size: var(--font-size-sm);
    }
    
    .footer-section h3 {
        font-size: var(--font-size-base);
    }
    
    .contact-item {
        font-size: var(--font-size-sm);
    }
    
    .footer-bottom p {
        font-size: var(--font-size-xs);
    }
}

/* Extra Small Mobile (320px and below) */
@media (max-width: 319px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .nav-container {
        padding: 0 var(--spacing-3);
    }
    
    .hero-title {
        font-size: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.25rem;
    }
    
    .service-card,
    .testimonial-card {
        padding: var(--spacing-4);
    }
    
    .btn {
        padding: var(--spacing-3) var(--spacing-5);
        font-size: var(--font-size-xs);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for retina displays */
    .hero-image img,
    .about-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .nav-toggle,
    .cta,
    .footer {
        display: none;
    }
    
    .hero {
        padding-top: var(--spacing-8);
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .hero-title {
        font-size: 24pt;
    }
    
    .section-header h2 {
        font-size: 18pt;
    }
    
    .btn {
        display: none;
    }
    
    .service-card,
    .testimonial-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}

/* Gallery Responsive Styles */
@media (max-width: 1023px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-6);
    }

    .map-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .map-placeholder iframe {
        height: 300px;
    }
}

@media (max-width: 767px) {
    /* Gallery Filter */
    .filter-buttons {
        gap: var(--spacing-2);
    }

    .filter-btn {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-sm);
    }

    /* Gallery Grid */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .gallery-image {
        height: 200px;
    }

    /* Contact Form */
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .contact-form {
        padding: var(--spacing-6);
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }

    /* Contact Info Grid */
    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    /* Working Hours */
    .working-hours-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }

    .hours-grid {
        gap: var(--spacing-3);
    }

    .hours-item {
        padding: var(--spacing-3);
    }

    /* Quick Contact */
    .quick-contact-buttons {
        flex-direction: column;
        gap: var(--spacing-4);
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
    }

    /* About Page */
    .story-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }

    .story-highlights {
        justify-content: center;
        gap: var(--spacing-6);
    }

    .mv-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .certifications-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .reasons-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    /* Services Page */
    .services-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-6);
    }

    .service-detail-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        padding: var(--spacing-8);
    }

    .service-detail-content.reverse {
        direction: ltr;
    }

    .service-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }

    .package-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }

    .brands-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-2);
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .guarantees-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    /* Lightbox Mobile */
    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
    }

    .lightbox-close {
        top: -30px;
        font-size: 25px;
    }

    .lightbox-prev,
    .lightbox-next {
        font-size: var(--font-size-lg);
        padding: var(--spacing-3);
        margin-left: -40px;
        margin-right: -40px;
    }

    .lightbox-caption {
        font-size: var(--font-size-base);
        padding: var(--spacing-3);
    }
}

@media (max-width: 479px) {
    /* Gallery */
    .gallery-image {
        height: 180px;
    }

    .gallery-overlay {
        padding: var(--spacing-4);
    }

    .gallery-info h3 {
        font-size: var(--font-size-base);
    }

    .gallery-info p {
        font-size: var(--font-size-xs);
    }

    /* Contact */
    .contact-card {
        padding: var(--spacing-6);
    }

    .contact-form {
        padding: var(--spacing-4);
    }

    /* Services */
    .services-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .service-detail-content {
        padding: var(--spacing-6);
    }

    .brands-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* About */
    .story-highlights {
        flex-direction: column;
        gap: var(--spacing-4);
    }

    .highlight-year {
        font-size: var(--font-size-2xl);
    }

    /* Lightbox */
    .lightbox-prev,
    .lightbox-next {
        display: none; /* Hide navigation arrows on very small screens */
    }

    .lightbox-caption {
        font-size: var(--font-size-sm);
    }
}

/* WhatsApp Button Responsive */
@media (max-width: 767px) {
    .whatsapp-float {
        width: 50px;
        height: 50px;
        font-size: 24px;
        bottom: 15px;
        left: 15px;
    }

    .whatsapp-float::before {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-2);
        right: 60px;
    }

    .whatsapp-float::after {
        right: 50px;
        border-width: 5px;
    }
}

@media (max-width: 479px) {
    .whatsapp-float {
        width: 45px;
        height: 45px;
        font-size: 20px;
        bottom: 10px;
        left: 10px;
    }

    /* Hide tooltip on very small screens */
    .whatsapp-float::before,
    .whatsapp-float::after {
        display: none;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .whatsapp-float i {
        animation: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-300: #4b5563;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    .header {
        background-color: var(--gray-100);
        border-bottom: 1px solid var(--gray-300);
    }
    
    .service-card,
    .testimonial-card {
        background-color: var(--gray-100);
        border-color: var(--gray-300);
    }
    
    .hero {
        background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    }
    
    .about-preview {
        background-color: var(--gray-100);
    }
}

/* Focus Styles for Accessibility */
.btn:focus,
.nav-link:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Hover Effects for Touch Devices */
@media (hover: none) and (pointer: coarse) {
    .service-card:hover,
    .btn:hover {
        transform: none;
    }
    
    .service-card:active {
        transform: scale(0.98);
    }
    
    .btn:active {
        transform: scale(0.95);
    }
}
