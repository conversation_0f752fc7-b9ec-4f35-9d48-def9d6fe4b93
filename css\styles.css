/* CSS Variables for Design System */
:root {
    /* Colors */
    --primary-blue: #1e40af;
    --primary-blue-dark: #1e3a8a;
    --secondary-blue: #3b82f6;
    --accent-blue: #60a5fa;
    --light-blue: #dbeafe;
    
    --gray-900: #111827;
    --gray-800: #1f2937;
    --gray-700: #374151;
    --gray-600: #4b5563;
    --gray-500: #6b7280;
    --gray-400: #9ca3af;
    --gray-300: #d1d5db;
    --gray-200: #e5e7eb;
    --gray-100: #f3f4f6;
    --gray-50: #f9fafb;
    
    --white: #ffffff;
    --black: #000000;
    
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --whatsapp: #25d366;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    
    /* Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: color var(--transition-fast);
}

ul {
    list-style: none;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    text-align: center;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background-color: var(--primary-blue);
    color: var(--white);
}

.btn-whatsapp {
    background-color: var(--whatsapp);
    color: var(--white);
}

.btn-whatsapp:hover {
    background-color: #128c7e;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Header */
.header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: var(--spacing-4) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-8);
}

.nav-link {
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    padding: var(--spacing-2) 0;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-blue);
    transition: width var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--gray-700);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: calc(80px + var(--spacing-20)) 0 var(--spacing-20);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--light-blue) 100%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Preview */
.services-preview {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
}

.service-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.service-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* About Preview */
.about-preview {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.about-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.feature i {
    color: var(--success);
    font-size: var(--font-size-lg);
}

.feature span {
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
}

.about-image img {
    width: 100%;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

/* Testimonials */
.testimonials {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.testimonial-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.testimonial-content {
    margin-bottom: var(--spacing-6);
}

.testimonial-content p {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    font-style: italic;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.author-info span {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.rating {
    display: flex;
    gap: var(--spacing-1);
}

.rating i {
    color: #fbbf24;
    font-size: var(--font-size-sm);
}

/* CTA Section */
.cta {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-4);
}

.cta-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    flex-wrap: wrap;
}

.cta .btn-primary {
    background-color: var(--white);
    color: var(--primary-blue);
}

.cta .btn-primary:hover {
    background-color: var(--gray-100);
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-4);
}

.footer-logo i {
    font-size: var(--font-size-2xl);
    color: var(--secondary-blue);
}

.footer-section p {
    color: var(--gray-400);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-4);
    color: var(--white);
}

.footer-section ul li {
    margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--secondary-blue);
}

.social-links {
    display: flex;
    gap: var(--spacing-4);
}

.social-links a {
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--secondary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
}

.contact-item i {
    color: var(--secondary-blue);
    margin-top: var(--spacing-1);
    min-width: 16px;
}

.contact-item span {
    color: var(--gray-400);
    line-height: 1.5;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-8);
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Page Header */
.page-header {
    padding: calc(80px + var(--spacing-16)) 0 var(--spacing-16);
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    text-align: center;
}

.page-header-content h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-4);
}

.page-header-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
    opacity: 0.9;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--white);
}

/* Services Overview */
.services-overview {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.services-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-8);
    margin-top: var(--spacing-16);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-8);
    background-color: var(--gray-50);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-2);
}

.stat-label {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

/* Detailed Services */
.detailed-services {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.service-detail {
    margin-bottom: var(--spacing-20);
}

.service-detail:last-child {
    margin-bottom: 0;
}

.service-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
    background-color: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.service-detail-content.reverse {
    direction: rtl;
}

.service-detail-content.reverse > * {
    direction: ltr;
}

.service-detail-text h3 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.service-detail-text p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);
}

.service-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.feature-item i {
    color: var(--success);
    font-size: var(--font-size-lg);
    min-width: 20px;
}

.feature-item span {
    color: var(--gray-700);
    font-weight: var(--font-weight-medium);
}

.service-pricing {
    background-color: var(--light-blue);
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-6);
}

.service-pricing h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-3);
}

.service-pricing p {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.service-pricing small {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.service-packages {
    margin-top: var(--spacing-6);
}

.service-packages h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.package-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-4);
}

.package-item {
    background-color: var(--gray-50);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    text-align: center;
    border: 2px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.package-item:hover {
    border-color: var(--primary-blue);
    transform: translateY(-2px);
}

.package-item h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.package-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    display: block;
    margin-bottom: var(--spacing-2);
}

.package-item p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.parts-brands {
    margin-top: var(--spacing-6);
}

.parts-brands h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-3);
}

.brands-grid span {
    background-color: var(--gray-100);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius);
    text-align: center;
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.bodywork-process {
    margin-top: var(--spacing-6);
}

.bodywork-process h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.process-step {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    flex-shrink: 0;
}

.step-content h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.step-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.service-detail-image img {
    width: 100%;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

/* Service Guarantees */
.service-guarantees {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.guarantees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
}

.guarantee-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.guarantee-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.guarantee-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--success), #059669);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.guarantee-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.guarantee-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.guarantee-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* About Page Styles */
.company-story {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.story-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.story-text p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.story-highlights {
    display: flex;
    gap: var(--spacing-8);
    margin-top: var(--spacing-8);
}

.highlight-item {
    text-align: center;
}

.highlight-year {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-2);
}

.highlight-text {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

.story-image img {
    width: 100%;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

/* Mission & Vision */
.mission-vision {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-8);
}

.mv-card {
    background-color: var(--white);
    padding: var(--spacing-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
}

.mv-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.mv-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.mv-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.mv-card p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: 1.6;
}

/* Company Values */
.company-values {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.value-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.value-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.value-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.value-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Team Section */
.team-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.team-member {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.team-member:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.member-image {
    height: 250px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: var(--spacing-6);
}

.member-info h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.member-position {
    font-size: var(--font-size-base);
    color: var(--primary-blue);
    font-weight: var(--font-weight-medium);
    display: block;
    margin-bottom: var(--spacing-4);
}

.member-info p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.member-certifications {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
}

.member-certifications span {
    background-color: var(--light-blue);
    color: var(--primary-blue);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* Certifications */
.certifications {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
}

.cert-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.cert-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.cert-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.cert-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.cert-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.cert-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Why Choose Us */
.why-choose-us {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.reason-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-6);
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.reason-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.reason-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    flex-shrink: 0;
}

.reason-content h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.reason-content p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Contact Page Styles */
.contact-info-section {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
}

.contact-card {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.contact-card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.contact-card-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.contact-card h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.contact-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.contact-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    background-color: var(--primary-blue);
    color: var(--white);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    text-decoration: none;
}

.contact-link:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
}

.contact-link.whatsapp {
    background-color: var(--whatsapp);
}

.contact-link.whatsapp:hover {
    background-color: #128c7e;
}

/* Working Hours */
.working-hours {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.working-hours-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.working-hours-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.working-hours-text p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);
}

.hours-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4);
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.hours-day {
    font-weight: var(--font-weight-medium);
    color: var(--gray-900);
}

.hours-time {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
}

.emergency-info {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background-color: var(--light-blue);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.emergency-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary-blue);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.emergency-icon i {
    color: var(--white);
    font-size: var(--font-size-lg);
}

.emergency-text h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.emergency-text p {
    color: var(--gray-700);
    line-height: 1.5;
    margin: 0;
}

.working-hours-image img {
    width: 100%;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

/* Contact Form */
.contact-form-section {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.contact-form-content {
    max-width: 800px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-12);
}

.form-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.form-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    line-height: 1.6;
}

.contact-form {
    background-color: var(--gray-50);
    padding: var(--spacing-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
}

.form-group {
    margin-bottom: var(--spacing-6);
}

.form-group label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: border-color var(--transition-fast);
    background-color: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    margin-bottom: var(--spacing-8);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-400);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-weight: bold;
    font-size: 12px;
}

.checkbox-text {
    color: var(--gray-700);
}

.form-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    flex-wrap: wrap;
}

.form-actions .btn {
    min-width: 160px;
}

/* Map Section */
.map-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.map-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-8);
    margin-top: var(--spacing-12);
}

.map-placeholder {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.map-placeholder iframe {
    width: 100%;
    height: 400px;
    border: none;
}

.map-info {
    background-color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    height: fit-content;
}

.map-info h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.transport-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.transport-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
}

.transport-item i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.transport-item h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.transport-item p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin: 0;
}

/* Quick Contact */
.quick-contact {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    text-align: center;
}

.quick-contact-content h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-4);
}

.quick-contact-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.quick-contact-buttons {
    display: flex;
    gap: var(--spacing-6);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-large {
    padding: var(--spacing-6) var(--spacing-8);
    font-size: var(--font-size-lg);
    min-width: 200px;
}

.btn-large span {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
}

.btn-large strong {
    font-weight: var(--font-weight-semibold);
}

.btn-large small {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* Gallery Page Styles */
.gallery-filter {
    padding: var(--spacing-12) 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    background-color: var(--gray-100);
    color: var(--gray-700);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background-color: var(--gray-200);
    border-color: var(--gray-300);
}

.filter-btn.active {
    background-color: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.filter-btn i {
    font-size: var(--font-size-sm);
}

/* Gallery Grid */
.gallery-grid-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.gallery-item {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all var(--transition-normal);
    opacity: 1;
    transform: scale(1);
}

.gallery-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.gallery-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-6);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    margin-top: auto;
}

.gallery-info h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: var(--spacing-2);
}

.gallery-info p {
    font-size: var(--font-size-sm);
    color: var(--white);
    opacity: 0.9;
    margin-bottom: var(--spacing-3);
}

.gallery-category {
    display: inline-block;
    background-color: var(--primary-blue);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.gallery-zoom {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.gallery-zoom:hover {
    background-color: var(--white);
    transform: scale(1.1);
}

.gallery-zoom i {
    color: var(--primary-blue);
    font-size: var(--font-size-base);
}

.load-more-container {
    text-align: center;
}

.load-more-btn {
    min-width: 200px;
}

/* Gallery Stats */
.gallery-stats {
    padding: var(--spacing-20) 0;
    background-color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-8);
}

.stat-card {
    text-align: center;
    padding: var(--spacing-8);
    background-color: var(--gray-50);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
}

.stat-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.stat-label {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    font-weight: var(--font-weight-medium);
}

/* Lightbox */
.lightbox {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease-in-out;
}

.lightbox.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    animation: zoomIn 0.3s ease-in-out;
}

.lightbox-image {
    width: 100%;
    height: auto;
    max-height: 80vh;
    object-fit: contain;
    border-radius: var(--radius-lg);
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: var(--white);
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.lightbox-close:hover {
    color: var(--gray-400);
}

.lightbox-caption {
    color: var(--white);
    text-align: center;
    padding: var(--spacing-4);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--white);
    font-size: var(--font-size-2xl);
    padding: var(--spacing-4);
    cursor: pointer;
    border-radius: var(--radius);
    transition: all var(--transition-fast);
    pointer-events: all;
    backdrop-filter: blur(10px);
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.lightbox-prev {
    margin-left: -60px;
}

.lightbox-next {
    margin-right: -60px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-4 {
    margin-bottom: var(--spacing-4);
}

.mb-8 {
    margin-bottom: var(--spacing-8);
}

.mt-8 {
    margin-top: var(--spacing-8);
}

.hidden {
    display: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* WhatsApp Floating Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    z-index: 1000;
    transition: all var(--transition-base);
    text-decoration: none;
    color: white;
    font-size: 28px;
}

.whatsapp-float:hover {
    background: #128c7e;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
    color: white;
    text-decoration: none;
}

.whatsapp-float:active {
    transform: scale(0.95);
}

.whatsapp-float i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* WhatsApp Tooltip */
.whatsapp-float::before {
    content: "WhatsApp ile iletişime geç";
    position: absolute;
    right: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--color-dark);
    color: white;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
    pointer-events: none;
}

.whatsapp-float::after {
    content: "";
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-left-color: var(--color-dark);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
}

.whatsapp-float:hover::before,
.whatsapp-float:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Print Styles */
@media print {
    .header,
    .nav-toggle,
    .cta,
    .footer,
    .btn,
    .gallery-zoom,
    .lightbox {
        display: none !important;
    }

    .hero {
        padding-top: var(--spacing-8);
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    .hero-title {
        font-size: 24pt;
    }

    .section-header h2 {
        font-size: 18pt;
    }

    .service-card,
    .testimonial-card,
    .contact-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .gallery-image {
        height: auto;
    }

    .gallery-overlay {
        position: static;
        background: none;
        opacity: 1;
        padding: 0.5rem;
    }

    .gallery-info h3,
    .gallery-info p {
        color: #000;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: var(--spacing-4);
}

.mb-8 {
    margin-bottom: var(--spacing-8);
}

.mt-8 {
    margin-top: var(--spacing-8);
}

.hidden {
    display: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
