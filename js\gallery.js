// Gallery functionality for Klinik Auto website

document.addEventListener('DOMContentLoaded', function() {
    initGalleryFilter();
    initLightbox();
    initLoadMore();
});

// Gallery Filter Functionality
function initGalleryFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            filterGalleryItems(galleryItems, filter);
        });
    });
}

function filterGalleryItems(items, filter) {
    items.forEach(item => {
        const category = item.getAttribute('data-category');
        
        if (filter === 'all' || category === filter) {
            item.style.display = 'block';
            setTimeout(() => {
                item.classList.remove('hidden');
            }, 10);
        } else {
            item.classList.add('hidden');
            setTimeout(() => {
                item.style.display = 'none';
            }, 300);
        }
    });
    
    // Update visible items count for load more functionality
    updateVisibleItemsCount();
}

// Lightbox Functionality
let currentImageIndex = 0;
let galleryImages = [];

function initLightbox() {
    // Collect all gallery images
    const galleryItems = document.querySelectorAll('.gallery-item');
    galleryImages = Array.from(galleryItems).map(item => {
        const img = item.querySelector('img');
        const title = item.querySelector('.gallery-info h3').textContent;
        return {
            src: img.src,
            title: title,
            element: item
        };
    });
    
    // Close lightbox when clicking outside image
    const lightbox = document.getElementById('lightbox');
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        const lightbox = document.getElementById('lightbox');
        if (lightbox.classList.contains('active')) {
            switch(e.key) {
                case 'Escape':
                    closeLightbox();
                    break;
                case 'ArrowLeft':
                    prevImage();
                    break;
                case 'ArrowRight':
                    nextImage();
                    break;
            }
        }
    });
}

function openLightbox(imageSrc, imageTitle) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxCaption = document.getElementById('lightboxCaption');
    
    // Find current image index
    currentImageIndex = galleryImages.findIndex(img => img.src === imageSrc);
    
    lightboxImage.src = imageSrc;
    lightboxImage.alt = imageTitle;
    lightboxCaption.textContent = imageTitle;
    
    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Preload adjacent images
    preloadAdjacentImages();
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.remove('active');
    document.body.style.overflow = '';
}

function nextImage() {
    if (currentImageIndex < galleryImages.length - 1) {
        currentImageIndex++;
    } else {
        currentImageIndex = 0; // Loop to first image
    }
    updateLightboxImage();
}

function prevImage() {
    if (currentImageIndex > 0) {
        currentImageIndex--;
    } else {
        currentImageIndex = galleryImages.length - 1; // Loop to last image
    }
    updateLightboxImage();
}

function updateLightboxImage() {
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxCaption = document.getElementById('lightboxCaption');
    const currentImage = galleryImages[currentImageIndex];
    
    // Add fade effect
    lightboxImage.style.opacity = '0';
    
    setTimeout(() => {
        lightboxImage.src = currentImage.src;
        lightboxImage.alt = currentImage.title;
        lightboxCaption.textContent = currentImage.title;
        lightboxImage.style.opacity = '1';
    }, 150);
    
    preloadAdjacentImages();
}

function preloadAdjacentImages() {
    // Preload next and previous images for smooth navigation
    const preloadIndexes = [
        currentImageIndex - 1 >= 0 ? currentImageIndex - 1 : galleryImages.length - 1,
        currentImageIndex + 1 < galleryImages.length ? currentImageIndex + 1 : 0
    ];
    
    preloadIndexes.forEach(index => {
        const img = new Image();
        img.src = galleryImages[index].src;
    });
}

// Load More Functionality
let visibleItemsCount = 12; // Initial number of visible items
const itemsPerLoad = 6; // Number of items to load each time

function initLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    // Initially hide items beyond the visible count
    updateVisibleItems();
    
    loadMoreBtn.addEventListener('click', function() {
        visibleItemsCount += itemsPerLoad;
        updateVisibleItems();
        
        // Smooth scroll to new items
        setTimeout(() => {
            const newlyVisibleItem = document.querySelector(`.gallery-item:nth-child(${visibleItemsCount - itemsPerLoad + 1})`);
            if (newlyVisibleItem) {
                newlyVisibleItem.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            }
        }, 100);
    });
}

function updateVisibleItems() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const visibleItems = Array.from(galleryItems).filter(item => 
        item.style.display !== 'none' && !item.classList.contains('hidden')
    );
    
    visibleItems.forEach((item, index) => {
        if (index < visibleItemsCount) {
            item.style.display = 'block';
            item.classList.remove('hidden');
        } else {
            item.style.display = 'none';
        }
    });
    
    // Hide load more button if all items are visible
    if (visibleItemsCount >= visibleItems.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-flex';
    }
}

function updateVisibleItemsCount() {
    // Reset visible items count when filter changes
    visibleItemsCount = 12;
    updateVisibleItems();
}

// Lazy Loading for Images
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Gallery Animation on Scroll
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = `${Math.random() * 0.3}s`;
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe gallery items for animation
    document.querySelectorAll('.gallery-item').forEach(item => {
        observer.observe(item);
    });
}

// Search Functionality (if needed in future)
function initGallerySearch() {
    const searchInput = document.getElementById('gallerySearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const galleryItems = document.querySelectorAll('.gallery-item');
            
            galleryItems.forEach(item => {
                const title = item.querySelector('.gallery-info h3').textContent.toLowerCase();
                const description = item.querySelector('.gallery-info p').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    item.style.display = 'block';
                    item.classList.remove('hidden');
                } else {
                    item.classList.add('hidden');
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
            
            updateVisibleItemsCount();
        });
    }
}

// Touch/Swipe Support for Mobile Lightbox
function initTouchSupport() {
    let touchStartX = 0;
    let touchEndX = 0;
    
    const lightbox = document.getElementById('lightbox');
    
    lightbox.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });
    
    lightbox.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe left - next image
                nextImage();
            } else {
                // Swipe right - previous image
                prevImage();
            }
        }
    }
}

// Initialize all gallery features
document.addEventListener('DOMContentLoaded', function() {
    initGalleryFilter();
    initLightbox();
    initLoadMore();
    initLazyLoading();
    initScrollAnimations();
    initTouchSupport();
    
    // Initialize search if search input exists
    if (document.getElementById('gallerySearch')) {
        initGallerySearch();
    }
});

// Export functions for global access
window.openLightbox = openLightbox;
window.closeLightbox = closeLightbox;
window.nextImage = nextImage;
window.prevImage = prevImage;
